/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    color: #333;
}

/* Container */
.container {
    max-width: 600px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* Header */
header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    padding: 30px;
    text-align: center;
}

header h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 300;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Main content */
main {
    padding: 30px;
}

/* Form styles */
#todo-form {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    align-items: center;
}

#title {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 50px;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

#title:focus {
    border-color: #4facfe;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

#title::placeholder {
    color: #adb5bd;
}

button[type="submit"] {
    padding: 15px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

button[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

button[type="submit"]:active {
    transform: translateY(0);
}

/* Todo list */
#todo-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Todo item styles (for JavaScript-generated content) */
.todo-item {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 15px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.todo-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-color: #4facfe;
}

.todo-content {
    flex: 1;
}

.todo-title {
    font-size: 18px;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 5px;
}

.todo-id {
    font-size: 12px;
    color: #6c757d;
    background: #f8f9fa;
    padding: 2px 8px;
    border-radius: 10px;
    display: inline-block;
}

.todo-actions {
    display: flex;
    gap: 10px;
}

.btn-delete {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-delete:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-complete {
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-complete:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
}

.todo-item.completed {
    opacity: 0.7;
    background: #f8f9fa;
}

.todo-item.completed .todo-title {
    text-decoration: line-through;
    color: #6c757d;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #adb5bd;
}

.empty-state p {
    font-size: 1rem;
    color: #ced4da;
}

/* Loading state */
.loading {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .container {
        border-radius: 15px;
    }

    header {
        padding: 20px;
    }

    header h1 {
        font-size: 2rem;
    }

    main {
        padding: 20px;
    }

    #todo-form {
        flex-direction: column;
        gap: 15px;
    }

    #title {
        width: 100%;
    }

    button[type="submit"] {
        width: 100%;
    }

    .todo-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .todo-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Animation for new todos */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.todo-item.new {
    animation: slideIn 0.3s ease-out;
}

/* Focus styles for accessibility */
button:focus,
input:focus {
    outline: 2px solid #4facfe;
    outline-offset: 2px;
}

/* Smooth transitions */
* {
    transition: all 0.3s ease;
}